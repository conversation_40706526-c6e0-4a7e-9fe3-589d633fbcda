# Generated by Django 5.0.6 on 2025-05-11 07:45

from django.db import migrations, models

def add_is_promotion_column(apps, schema_editor):
    # Get the database connection
    connection = schema_editor.connection
    
    # Check if the column already exists
    cursor = connection.cursor()
    cursor.execute("PRAGMA table_info(game_message)")
    columns = cursor.fetchall()
    column_names = [column[1] for column in columns]
    
    # If is_promotion column doesn't exist, add it
    if 'is_promotion' not in column_names:
        cursor.execute("ALTER TABLE game_message ADD COLUMN is_promotion boolean DEFAULT 0")
        
    # Close the cursor
    cursor.close()

class Migration(migrations.Migration):

    dependencies = [
        ('game', '0003_message_is_promotion'),
    ]

    operations = [
        migrations.RunPython(add_is_promotion_column),
    ]
