/* Right Sidebar Styles */

/* Right sidebar container */
.right-sidebar {
    width: 300px;
    background-color: #f8f9fa;
    border-left: 1px solid #e0e0e0;
    padding: 20px;
    display: flex;
    flex-direction: column;
    overflow-y: hidden; /* Changed from auto to hidden */
    transition: transform 0.3s ease, width 0.3s ease;
}

/* Right sidebar toggle button */
.right-sidebar-toggle {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 24px;
    height: 18px;
    cursor: pointer;
    margin-left: 15px;
    z-index: 10;
    position: relative;
    transform: rotate(180deg); /* Rotate to differentiate from left sidebar toggle */
}

.right-sidebar-toggle span {
    display: block;
    height: 2px;
    width: 100%;
    background-color: #4a86e8; /* Different color to differentiate */
    border-radius: 2px;
    transition: all 0.3s ease;
}

/* Hover effect */
.right-sidebar-toggle:hover span {
    background-color: #2a66c8;
}

/* Active state when right sidebar is hidden */
.right-sidebar-hidden .right-sidebar-toggle span {
    background-color: #999;
}

/* Right sidebar header */
.right-sidebar-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
}

.right-sidebar-collapse-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    margin-right: 10px;
    margin-top: 5px;
    color: #999;
    font-size: 0.8rem;
    cursor: pointer;
    border-radius: 50%;
    background-color: #f0f0f0;
    transition: all 0.3s ease;
}

.right-sidebar-collapse-indicator:hover {
    background-color: #e0e0e0;
    color: #666;
}

.right-sidebar-title {
    flex: 1;
    font-size: 1.2rem;
    font-weight: bold;
    color: #333;
    text-align: center;
}

/* Company hierarchy section */
.right-sidebar .org-chart-container {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e0e0e0;
}

.right-sidebar .org-chart-container h3 {
    font-size: 1rem;
    margin-bottom: 10px;
    color: #4a86e8;
}

/* Career path section */
.right-sidebar .role-progression-container {
    margin-bottom: 20px;
}

.right-sidebar .role-progression-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.right-sidebar .role-progression-header h3 {
    font-size: 1rem;
    color: #4a86e8;
    margin: 0;
}

.right-sidebar .toggle-button {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    font-size: 1rem;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.right-sidebar .toggle-button:hover {
    color: #333;
}

/* Right sidebar hidden state */
.right-sidebar-hidden .right-sidebar {
    transform: translateX(300px);
    width: 0;
    padding: 0;
    overflow: hidden;
    border-left: none;
}

/* Adjust main content when right sidebar is hidden */
.right-sidebar-hidden .main-content {
    margin-right: 0;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .right-sidebar {
        width: 250px;
        padding: 15px;
    }

    .right-sidebar-hidden .right-sidebar {
        transform: translateX(250px);
    }
}

/* Tablet adjustments */
@media (min-width: 769px) and (max-width: 992px) {
    .app-container {
        flex-direction: column;
    }

    .main-content {
        width: 100%;
        min-width: 0;
    }

    /* Hide right sidebar completely on tablet */
    .right-sidebar {
        display: none;
    }

    /* Optimize main content for tablet */
    .messages-container {
        height: calc(100vh - 350px); /* Adjust for header, sidebar, and input area */
    }

    /* Improve input area on tablet */
    .input-area {
        padding: 15px;
    }

    /* Improve preview container on tablet */
    .preview-container {
        max-height: none;
        height: auto;
    }

    /* Make buttons more touch-friendly on tablet */
    button {
        min-height: 40px;
        padding: 8px 16px;
    }

    /* Improve spacing between buttons */
    .preview-actions, .edit-actions {
        gap: 12px;
    }
}

/* Mobile adjustments */
@media (max-width: 768px) {
    .app-container {
        flex-direction: column;
    }

    .main-content {
        width: 100%;
        min-height: 60vh;
    }

    /* Hide right sidebar completely on mobile */
    .right-sidebar {
        display: none;
    }

    /* Optimize main content for mobile */
    .messages-container {
        height: calc(100vh - 350px); /* Adjust for header, sidebar, and input area */
    }

    /* Improve input area on mobile */
    .input-area {
        padding: 10px;
    }

    .prompt-input {
        font-size: 16px; /* Prevent zoom on iOS */
    }

    /* Show right sidebar content in the left sidebar on mobile */
    .sidebar .mobile-right-sidebar-content {
        display: block;
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #e0e0e0;
    }
}

/* Small mobile adjustments */
@media (max-width: 480px) {
    .sidebar, .main-content {
        padding: 10px;
    }

    .header {
        padding: 10px;
    }

    /* Adjust sidebar height for small screens */
    .sidebar {
        max-height: 200px;
    }

    .messages-container {
        height: calc(100vh - 300px); /* Adjust for smaller sidebar and screen */
    }

    /* Make buttons more touch-friendly */
    button {
        min-height: 44px; /* Apple's recommended minimum touch target size */
    }

    /* Improve spacing for small screens */
    .preview-actions, .edit-actions {
        flex-direction: column;
        gap: 10px;
    }

    .preview-actions button, .edit-actions button {
        width: 100%;
    }

    /* Ensure mobile right sidebar content is compact */
    .mobile-right-sidebar-content {
        font-size: 0.9em;
    }

    .mobile-right-sidebar-content .org-chart-container,
    .mobile-right-sidebar-content .role-progression-container {
        margin-bottom: 10px;
        padding-bottom: 10px;
    }
}

/* Adjust app container to accommodate right sidebar */
.app-container {
    max-width: 1700px; /* Increased from 1400px to accommodate right sidebar */
}

/* Ensure main content doesn't shrink on desktop */
@media (min-width: 993px) {
    .main-content {
        min-width: 600px; /* Ensure main content has a minimum width on desktop */
        flex: 1;
    }
}
