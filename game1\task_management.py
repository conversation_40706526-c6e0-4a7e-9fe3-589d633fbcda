"""
Task management for the Context-Aware Game.
This module contains functions for managing tasks.
"""

import logging
from .utils import get_task_by_id, create_message, create_delayed_message, log_info
from .all_role_tasks import get_all_role_tasks

def get_current_task(game_state):
    """
    Get the current task for the player.

    Args:
        game_state (dict): The current game state

    Returns:
        dict or None: The current task or None if not found
    """
    current_role = game_state["current_role"]
    current_task_id = game_state["current_task"]

    # Get tasks for the current role
    role_tasks = get_all_role_tasks(current_role)

    # Find the current task in the role tasks
    current_task = get_task_by_id(current_task_id, role_tasks)

    # If task not found, use the appropriate task for the role
    if not current_task and role_tasks:
        # BUGFIX: The current task should be based on role_challenges_completed
        # If user has completed N tasks, they should be on task N (0-indexed)
        # This ensures the current task matches the user's actual progress
        task_index = game_state["role_challenges_completed"]

        if task_index < len(role_tasks):
            current_task = role_tasks[task_index]
            game_state["current_task"] = current_task["id"]
            game_state["current_manager"] = current_task["manager"]

            log_info(f"Task not found, setting based on progress: role_challenges_completed={game_state['role_challenges_completed']}, task_index={task_index}, task_id={current_task['id']}")
        else:
            log_info(f"User has completed all tasks for role (role_challenges_completed={game_state['role_challenges_completed']}, available_tasks={len(role_tasks)})")
            # Don't set a task - user should be promoted

    return current_task

def get_next_task(game_state):
    """
    Get the next task for the player.

    Args:
        game_state (dict): The current game state

    Returns:
        dict or None: The next task or None if there are no more tasks
    """
    current_role = game_state["current_role"]
    current_task_id = game_state["current_task"]

    # Get tasks for the current role
    role_tasks = get_all_role_tasks(current_role)

    # Find the current task index
    current_task_index = 0
    for i, task in enumerate(role_tasks):
        if task["id"] == current_task_id:
            current_task_index = i
            break

    # Calculate the next task index
    next_task_index = current_task_index + 1

    # Check if there are more tasks available for this role
    if next_task_index < len(role_tasks):
        return role_tasks[next_task_index]

    return None

def prepare_first_task(game_state):
    """
    Prepare the first task for the player.

    Args:
        game_state (dict): The current game state

    Returns:
        dict: The updated game state with the first task prepared
    """
    # Get the first task for the applicant role
    first_task = get_all_role_tasks("applicant")[0]

    # Store first task info in game_state but don't add it to messages yet
    # It will be fetched by the client after a delay
    game_state["pending_first_task"] = {
        "task": first_task,
        "id": first_task["id"]
    }

    log_info(f"First task prepared but not added yet: {first_task}")

    return game_state

def add_next_task_to_game_state(game_state, next_task, delay_seconds=5):
    """
    Add the next task to the game state.

    Args:
        game_state (dict): The current game state
        next_task (dict): The next task
        delay_seconds (int, optional): The delay in seconds. Defaults to 5.

    Returns:
        dict: The updated game state
    """
    # Update the game state with the next task
    game_state["current_task"] = next_task["id"]
    game_state["current_manager"] = next_task["manager"]

    log_info(f"Next task selected: id={next_task['id']}, manager={next_task['manager']}")

    # Create a delayed message for the next task
    next_challenge = create_delayed_message(
        next_task["manager"],
        next_task["description"],
        delay_seconds,
        is_challenge=True,
        task_id=next_task["id"]
    )

    # Add the message to the game state
    game_state["messages"].append(next_challenge)

    log_info(f"Added next task challenge from {next_task['manager']} with {delay_seconds}-second delay")

    return game_state

def prepare_next_task_after_promotion(game_state, next_role):
    """
    Prepare the next task after a promotion.

    Args:
        game_state (dict): The current game state
        next_role (str): The next role

    Returns:
        dict: The updated game state with the next task prepared
    """
    # Get the first task for the new role
    new_role_tasks = get_all_role_tasks(next_role)

    if not new_role_tasks:
        log_info(f"No tasks found for role {next_role}")
        game_state["game_completed"] = True
        game_state["current_task"] = None
        return game_state

    next_task = new_role_tasks[0]
    game_state["current_task"] = next_task["id"]
    game_state["current_manager"] = next_task["manager"]

    # Store the next task info but don't add it yet
    # It will be fetched by the client after a delay
    game_state["next_task_pending"] = True
    game_state["pending_next_task"] = {
        "task": next_task,
        "id": next_task["id"]
    }

    log_info(f"Next task after promotion prepared but not added yet: {next_task}")

    return game_state

def add_promotion_message_to_game_state(game_state, current_role, next_role):
    """
    Add a promotion message to the game state.

    Args:
        game_state (dict): The current game state
        current_role (str): The current role
        next_role (str): The next role

    Returns:
        dict: The updated game state
    """
    from .role_progression import get_promotion_message

    # Get the promotion message
    promotion_message = get_promotion_message(current_role)

    # Create a promotion message
    promotion_msg = create_message(
        game_state["current_manager"],
        f"## {promotion_message}",
        is_promotion=True
    )

    # Add the message to the game state
    game_state["messages"].append(promotion_msg)

    log_info(f"Added promotion message from {current_role} to {next_role}")

    return game_state
